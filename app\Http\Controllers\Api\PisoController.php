<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Piso;
use App\Models\PeriodoPiso;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Validation\Rule;

class PisoController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request): JsonResponse
    {
        $query = Piso::with(['periodoActivo.cliente', 'documentos']);

        // Filtros
        if ($request->has('disponible')) {
            $query->where('disponible', $request->boolean('disponible'));
        }

        if ($request->has('tipo_piso')) {
            $query->where('tipo_piso', $request->tipo_piso);
        }

        if ($request->has('ciudad')) {
            $query->where('ciudad', 'like', '%' . $request->ciudad . '%');
        }

        if ($request->has('precio_min')) {
            $query->where('precio_mensual', '>=', $request->precio_min);
        }

        if ($request->has('precio_max')) {
            $query->where('precio_mensual', '<=', $request->precio_max);
        }

        $pisos = $query->paginate($request->get('per_page', 15));

        return response()->json($pisos);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'numero_piso' => 'required|string|max:255',
            'direccion' => 'required|string|max:255',
            'direccion2' => 'nullable|string|max:255',
            'cp' => 'required|string|max:10',
            'ciudad' => 'required|string|max:255',
            'provincia' => 'nullable|string|max:255',
            'habitaciones' => 'required|integer|min:1',
            'banos' => 'required|integer|min:1',
            'metros_cuadrados' => 'required|numeric|min:1',
            'descripcion' => 'nullable|string',
            'precio_mensual' => 'required|numeric|min:0',
            'fianza' => 'nullable|numeric|min:0',
            'amueblado' => 'boolean',
            'disponible' => 'boolean',
            'tipo_piso' => ['required', Rule::in(['estudio', 'apartamento', 'piso', 'atico', 'duplex'])],
            'caracteristicas' => 'nullable|array',
            'propietario_nombre' => 'nullable|string|max:255',
            'propietario_telefono' => 'nullable|string|max:20',
            'propietario_email' => 'nullable|email|max:255',
        ]);

        $piso = Piso::create($validated);

        return response()->json($piso->load(['documentos']), 201);
    }

    /**
     * Display the specified resource.
     */
    public function show(Piso $piso): JsonResponse
    {
        return response()->json($piso->load([
            'periodos.cliente',
            'periodoActivo.cliente',
            'documentos.usuario'
        ]));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Piso $piso): JsonResponse
    {
        $validated = $request->validate([
            'numero_piso' => 'sometimes|string|max:255',
            'direccion' => 'sometimes|string|max:255',
            'direccion2' => 'nullable|string|max:255',
            'cp' => 'sometimes|string|max:10',
            'ciudad' => 'sometimes|string|max:255',
            'provincia' => 'nullable|string|max:255',
            'habitaciones' => 'sometimes|integer|min:1',
            'banos' => 'sometimes|integer|min:1',
            'metros_cuadrados' => 'sometimes|numeric|min:1',
            'descripcion' => 'nullable|string',
            'precio_mensual' => 'sometimes|numeric|min:0',
            'fianza' => 'nullable|numeric|min:0',
            'amueblado' => 'boolean',
            'disponible' => 'boolean',
            'tipo_piso' => ['sometimes', Rule::in(['estudio', 'apartamento', 'piso', 'atico', 'duplex'])],
            'caracteristicas' => 'nullable|array',
            'propietario_nombre' => 'nullable|string|max:255',
            'propietario_telefono' => 'nullable|string|max:20',
            'propietario_email' => 'nullable|email|max:255',
        ]);

        $piso->update($validated);

        return response()->json($piso->load(['documentos']));
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Piso $piso): JsonResponse
    {
        // Verificar que no tenga períodos activos
        if ($piso->periodoActivo) {
            return response()->json([
                'message' => 'No se puede eliminar un piso con período activo'
            ], 422);
        }

        $piso->delete();

        return response()->json(['message' => 'Piso eliminado correctamente']);
    }

    /**
     * Get available pisos
     */
    public function disponibles(): JsonResponse
    {
        $pisos = Piso::disponibles()->with(['documentos'])->get();
        return response()->json($pisos);
    }

    /**
     * Get occupied pisos
     */
    public function ocupados(): JsonResponse
    {
        $pisos = Piso::ocupados()->with(['periodoActivo.cliente'])->get();
        return response()->json($pisos);
    }

    /**
     * Búsqueda avanzada de pisos
     */
    public function buscar(Request $request): JsonResponse
    {
        $query = Piso::with(['periodoActivo.cliente', 'documentos']);

        // Filtros de búsqueda
        if ($request->filled('q')) {
            $searchTerm = $request->q;
            $query->where(function ($q) use ($searchTerm) {
                $q->where('numero_piso', 'like', "%{$searchTerm}%")
                  ->orWhere('direccion', 'like', "%{$searchTerm}%")
                  ->orWhere('ciudad', 'like', "%{$searchTerm}%")
                  ->orWhere('descripcion', 'like', "%{$searchTerm}%");
            });
        }

        if ($request->filled('disponible')) {
            $query->where('disponible', $request->boolean('disponible'));
        }

        if ($request->filled('tipo_piso')) {
            $query->where('tipo_piso', $request->tipo_piso);
        }

        if ($request->filled('precio_min')) {
            $query->where('precio_mensual', '>=', $request->precio_min);
        }

        if ($request->filled('precio_max')) {
            $query->where('precio_mensual', '<=', $request->precio_max);
        }

        if ($request->filled('habitaciones')) {
            $query->where('habitaciones', $request->habitaciones);
        }

        if ($request->filled('banos')) {
            $query->where('banos', $request->banos);
        }

        if ($request->filled('amueblado')) {
            $query->where('amueblado', $request->boolean('amueblado'));
        }

        if ($request->filled('metros_min')) {
            $query->where('metros_cuadrados', '>=', $request->metros_min);
        }

        if ($request->filled('metros_max')) {
            $query->where('metros_cuadrados', '<=', $request->metros_max);
        }

        if ($request->filled('ciudad')) {
            $query->where('ciudad', 'like', "%{$request->ciudad}%");
        }

        if ($request->filled('cp')) {
            $query->where('cp', $request->cp);
        }

        // Ordenamiento
        $sortBy = $request->get('sort_by', 'numero_piso');
        $sortOrder = $request->get('sort_order', 'asc');
        $query->orderBy($sortBy, $sortOrder);

        $pisos = $query->paginate($request->get('per_page', 15));

        return response()->json($pisos);
    }

    /**
     * Obtener historial de un piso
     */
    public function historial(Piso $piso): JsonResponse
    {
        $historial = $piso->periodos()
                          ->with(['cliente'])
                          ->orderBy('date_from', 'desc')
                          ->get();

        return response()->json($historial);
    }

    /**
     * Cambiar estado de disponibilidad de un piso
     */
    public function cambiarEstado(Request $request, Piso $piso): JsonResponse
    {
        $validated = $request->validate([
            'disponible' => 'required|boolean',
            'motivo' => 'nullable|string|max:255'
        ]);

        $piso->update(['disponible' => $validated['disponible']]);

        return response()->json([
            'message' => 'Estado del piso actualizado correctamente',
            'piso' => $piso->load(['documentos'])
        ]);
    }

    /**
     * Obtener estadísticas de pisos
     */
    public function estadisticas(): JsonResponse
    {
        $stats = [
            'total' => Piso::count(),
            'disponibles' => Piso::where('disponible', true)->count(),
            'ocupados' => Piso::where('disponible', false)->count(),
            'por_tipo' => Piso::selectRaw('tipo_piso, COUNT(*) as total')
                             ->groupBy('tipo_piso')
                             ->get(),
            'por_habitaciones' => Piso::selectRaw('habitaciones, COUNT(*) as total')
                                     ->groupBy('habitaciones')
                                     ->orderBy('habitaciones')
                                     ->get(),
            'precio_promedio' => Piso::avg('precio_mensual'),
            'precio_min' => Piso::min('precio_mensual'),
            'precio_max' => Piso::max('precio_mensual'),
            'metros_promedio' => Piso::avg('metros_cuadrados'),
            'ocupacion_porcentaje' => round((Piso::where('disponible', false)->count() / Piso::count()) * 100, 2)
        ];

        return response()->json($stats);
    }
}
