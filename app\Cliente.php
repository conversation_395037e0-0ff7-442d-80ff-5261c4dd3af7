<?php

namespace App;

use Illuminate\Database\Eloquent\Model;
use App\Models\PeriodoPiso;
//use Illuminate\Database\Eloquent\SoftDeletes;

class Cliente extends Model
{
    // use SoftDeletes;

    protected $fillable = [
        'nombre', 'apellido', 'documento', 'descripcion', 'status', 'total_pago', 'vencimiento', 'posible_cliente', 'telefono'
        , 'direccion', 'direccion2', 'cp', 'ciudad', 'fianza'
    ];

    protected $casts = [
        'vencimiento' => 'date',
        'total_pago' => 'decimal:2',
        'fianza' => 'decimal:2',
        'posible_cliente' => 'boolean',
        'status' => 'boolean'
    ];

    // Relaciones existentes
    public function periodos() {
        return $this->hasMany(Periodo::class, 'id_cliente', 'id');
    }

    // Nuevas relaciones
    public function periodosActivos() {
        return $this->hasMany(Periodo::class, 'id_cliente', 'id')->whereNull('date_to');
    }

    public function periodosPisos() {
        return $this->hasMany(\App\Models\PeriodoPiso::class, 'id_cliente');
    }

    public function periodosPisosActivos() {
        return $this->hasMany(\App\Models\PeriodoPiso::class, 'id_cliente')->whereNull('date_to');
    }

    public function alquileres() {
        return $this->hasMany(\App\Alquiler::class, 'id_cliente');
    }

    // Scopes
    public function scopeActivos($query) {
        return $query->where('status', true);
    }

    public function scopePosiblesClientes($query) {
        return $query->where('posible_cliente', true);
    }

    public function scopeClientes($query) {
        return $query->where('posible_cliente', false);
    }

    // Métodos auxiliares
    public function getNombreCompletoAttribute()
    {
        return $this->nombre . ' ' . $this->apellido;
    }

    public function tieneAlquileresActivos() {
        return $this->periodosActivos()->count() > 0 || $this->periodosPisosActivos()->count() > 0;
    }
}

