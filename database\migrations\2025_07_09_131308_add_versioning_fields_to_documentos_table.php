<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('documentos', function (Blueprint $table) {
            // Campos para versionado de documentos
            $table->unsignedBigInteger('documento_padre_id')->nullable();
            $table->string('motivo_cambio')->nullable();
            $table->integer('version')->default(1);

            // Campos adicionales para compatibilidad con el controlador
            $table->string('ruta_archivo')->nullable(); // alias para archivo_path
            $table->string('tipo_archivo')->nullable(); // alias para mime_type

            // Índices
            $table->foreign('documento_padre_id')->references('id')->on('documentos')->onDelete('cascade');
            $table->index('documento_padre_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('documentos', function (Blueprint $table) {
            $table->dropForeign(['documento_padre_id']);
            $table->dropColumn([
                'documento_padre_id',
                'motivo_cambio',
                'version',
                'ruta_archivo',
                'tipo_archivo'
            ]);
        });
    }
};
