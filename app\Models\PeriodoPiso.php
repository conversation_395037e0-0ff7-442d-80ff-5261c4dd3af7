<?php

namespace App\Models;

use App\Cliente;
use App\Alquiler;
use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class PeriodoPiso extends Model
{
    use HasFactory;

    protected $table = 'periodos_pisos';

    protected $fillable = [
        'id_piso', 
        'id_cliente', 
        'date_from', 
        'date_to', 
        'fecha_inicio', 
        'fecha_fin',
        'fianza', 
        'fianza_pagada',
        'fecha_fianza',
        'precio_mensual', 
        'observaciones',
        'estado',
        'condiciones_especiales'
    ];

    protected $casts = [
        'date_from' => 'date',
        'date_to' => 'date',
        'fecha_inicio' => 'date',
        'fecha_fin' => 'date',
        'fecha_fianza' => 'date',
        'fianza' => 'decimal:2',
        'fianza_pagada' => 'decimal:2',
        'precio_mensual' => 'decimal:2',
        'condiciones_especiales' => 'json'
    ];

    // Relaciones
    public function piso()
    {
        return $this->belongsTo(Piso::class, 'id_piso');
    }

    public function cliente()
    {
        return $this->belongsTo(Cliente::class, 'id_cliente');
    }

    public function alquileres()
    {
        return $this->morphMany(Alquiler::class, 'alquilable');
    }

    // Scopes
    public function scopeActivos($query)
    {
        // Compatibilidad con ambas estructuras de tabla
        if (Schema::hasColumn('periodos_pisos', 'estado')) {
            return $query->where('estado', 'activo');
        } else {
            return $query->whereNull('date_to');
        }
    }

    public function scopeFinalizados($query)
    {
        // Compatibilidad con ambas estructuras de tabla
        if (Schema::hasColumn('periodos_pisos', 'estado')) {
            return $query->where('estado', 'finalizado');
        } else {
            return $query->whereNotNull('date_to');
        }
    }

    // Métodos auxiliares
    public function estaActivo()
    {
        if (Schema::hasColumn('periodos_pisos', 'estado')) {
            return $this->estado === 'activo';
        } else {
            return is_null($this->date_to);
        }
    }
}



