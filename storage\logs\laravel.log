[2025-07-09 11:11:14] local.DEBUG: SanctumCors  
[2025-07-09 11:11:14] local.DEBUG: http://localhost:3000  
[2025-07-09 11:11:15] local.ERROR: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'barnatrasteros.alquilers' doesn't exist (Connection: mysql, SQL: select count(*) as aggregate from `alquilers` where `estado_pago` = pendiente) {"userId":2,"exception":"[object] (Illuminate\\Database\\QueryException(code: 42S02): SQLSTATE[42S02]: Base table or view not found: 1146 Table 'barnatrasteros.alquilers' doesn't exist (Connection: mysql, SQL: select count(*) as aggregate from `alquilers` where `estado_pago` = pendiente) at C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:822)
[stacktrace]
#0 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback()
#1 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(395): Illuminate\\Database\\Connection->run()
#2 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3131): Illuminate\\Database\\Connection->select()
#3 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3116): Illuminate\\Database\\Query\\Builder->runSelect()
#4 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3706): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#5 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3115): Illuminate\\Database\\Query\\Builder->onceWithColumns()
#6 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3630): Illuminate\\Database\\Query\\Builder->get()
#7 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3558): Illuminate\\Database\\Query\\Builder->aggregate()
#8 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(2205): Illuminate\\Database\\Query\\Builder->count()
#9 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\app\\Http\\Controllers\\Api\\DashboardController.php(40): Illuminate\\Database\\Eloquent\\Builder->__call()
#10 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\Api\\DashboardController->resumen()
#11 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction()
#12 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch()
#13 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#14 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#15 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}()
#16 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#17 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle()
#18 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(63): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#19 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Auth\\Middleware\\Authenticate->handle()
#20 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(63): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#21 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Auth\\Middleware\\Authenticate->handle()
#22 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\sanctum\\src\\Http\\Middleware\\EnsureFrontendRequestsAreStateful.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#23 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Laravel\\Sanctum\\Http\\Middleware\\EnsureFrontendRequestsAreStateful->Laravel\\Sanctum\\Http\\Middleware\\{closure}()
#24 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#25 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle()
#26 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(120): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#27 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest()
#28 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle()
#29 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#30 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle()
#31 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#32 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle()
#33 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\sanctum\\src\\Http\\Middleware\\EnsureFrontendRequestsAreStateful.php(61): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#34 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(190): Laravel\\Sanctum\\Http\\Middleware\\EnsureFrontendRequestsAreStateful->Laravel\\Sanctum\\Http\\Middleware\\{closure}()
#35 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#36 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\sanctum\\src\\Http\\Middleware\\EnsureFrontendRequestsAreStateful.php(25): Illuminate\\Pipeline\\Pipeline->then()
#37 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Laravel\\Sanctum\\Http\\Middleware\\EnsureFrontendRequestsAreStateful->handle()
#38 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#39 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then()
#40 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack()
#41 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute()
#42 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute()
#43 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch()
#44 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}()
#45 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#46 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#47 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#48 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#49 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#50 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#51 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#52 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle()
#53 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#54 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#55 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#56 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle()
#57 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#58 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#59 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#60 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle()
#61 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#62 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle()
#63 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#64 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then()
#65 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#66 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle()
#67 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('...')
#68 {main}

[previous exception] [object] (PDOException(code: 42S02): SQLSTATE[42S02]: Base table or view not found: 1146 Table 'barnatrasteros.alquilers' doesn't exist at C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:404)
[stacktrace]
#0 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(404): PDO->prepare()
#1 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(809): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}()
#2 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback()
#3 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(395): Illuminate\\Database\\Connection->run()
#4 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3131): Illuminate\\Database\\Connection->select()
#5 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3116): Illuminate\\Database\\Query\\Builder->runSelect()
#6 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3706): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#7 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3115): Illuminate\\Database\\Query\\Builder->onceWithColumns()
#8 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3630): Illuminate\\Database\\Query\\Builder->get()
#9 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3558): Illuminate\\Database\\Query\\Builder->aggregate()
#10 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(2205): Illuminate\\Database\\Query\\Builder->count()
#11 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\app\\Http\\Controllers\\Api\\DashboardController.php(40): Illuminate\\Database\\Eloquent\\Builder->__call()
#12 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\Api\\DashboardController->resumen()
#13 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction()
#14 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch()
#15 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#16 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#17 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}()
#18 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#19 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle()
#20 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(63): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#21 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Auth\\Middleware\\Authenticate->handle()
#22 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(63): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#23 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Auth\\Middleware\\Authenticate->handle()
#24 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\sanctum\\src\\Http\\Middleware\\EnsureFrontendRequestsAreStateful.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#25 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Laravel\\Sanctum\\Http\\Middleware\\EnsureFrontendRequestsAreStateful->Laravel\\Sanctum\\Http\\Middleware\\{closure}()
#26 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#27 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle()
#28 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(120): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#29 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest()
#30 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle()
#31 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#32 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle()
#33 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#34 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle()
#35 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\sanctum\\src\\Http\\Middleware\\EnsureFrontendRequestsAreStateful.php(61): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#36 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(190): Laravel\\Sanctum\\Http\\Middleware\\EnsureFrontendRequestsAreStateful->Laravel\\Sanctum\\Http\\Middleware\\{closure}()
#37 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#38 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\sanctum\\src\\Http\\Middleware\\EnsureFrontendRequestsAreStateful.php(25): Illuminate\\Pipeline\\Pipeline->then()
#39 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Laravel\\Sanctum\\Http\\Middleware\\EnsureFrontendRequestsAreStateful->handle()
#40 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#41 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then()
#42 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack()
#43 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute()
#44 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute()
#45 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch()
#46 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}()
#47 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#48 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#49 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#50 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#51 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#52 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#53 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#54 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle()
#55 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#56 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#57 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#58 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle()
#59 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#60 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#61 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#62 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle()
#63 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#64 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle()
#65 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#66 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then()
#67 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#68 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle()
#69 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('...')
#70 {main}
"} 
[2025-07-09 11:11:15] local.ERROR: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'barnatrasteros.alquilers' doesn't exist (Connection: mysql, SQL: select * from `alquilers` where `estado_pago` in (pendiente, vencido, parcial) order by `fecha_vencimiento` asc limit 10) {"userId":2,"exception":"[object] (Illuminate\\Database\\QueryException(code: 42S02): SQLSTATE[42S02]: Base table or view not found: 1146 Table 'barnatrasteros.alquilers' doesn't exist (Connection: mysql, SQL: select * from `alquilers` where `estado_pago` in (pendiente, vencido, parcial) order by `fecha_vencimiento` asc limit 10) at C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:822)
[stacktrace]
#0 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback()
#1 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(395): Illuminate\\Database\\Connection->run()
#2 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3131): Illuminate\\Database\\Connection->select()
#3 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3116): Illuminate\\Database\\Query\\Builder->runSelect()
#4 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3706): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#5 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3115): Illuminate\\Database\\Query\\Builder->onceWithColumns()
#6 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(872): Illuminate\\Database\\Query\\Builder->get()
#7 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(854): Illuminate\\Database\\Eloquent\\Builder->getModels()
#8 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\app\\Http\\Controllers\\Api\\DashboardController.php(70): Illuminate\\Database\\Eloquent\\Builder->get()
#9 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\Api\\DashboardController->pagosPendientes()
#10 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction()
#11 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch()
#12 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#13 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#14 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}()
#15 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#16 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle()
#17 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(63): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#18 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Auth\\Middleware\\Authenticate->handle()
#19 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(63): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#20 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Auth\\Middleware\\Authenticate->handle()
#21 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\sanctum\\src\\Http\\Middleware\\EnsureFrontendRequestsAreStateful.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#22 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Laravel\\Sanctum\\Http\\Middleware\\EnsureFrontendRequestsAreStateful->Laravel\\Sanctum\\Http\\Middleware\\{closure}()
#23 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#24 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle()
#25 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(120): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#26 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest()
#27 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle()
#28 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#29 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle()
#30 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#31 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle()
#32 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\sanctum\\src\\Http\\Middleware\\EnsureFrontendRequestsAreStateful.php(61): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#33 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(190): Laravel\\Sanctum\\Http\\Middleware\\EnsureFrontendRequestsAreStateful->Laravel\\Sanctum\\Http\\Middleware\\{closure}()
#34 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#35 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\sanctum\\src\\Http\\Middleware\\EnsureFrontendRequestsAreStateful.php(25): Illuminate\\Pipeline\\Pipeline->then()
#36 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Laravel\\Sanctum\\Http\\Middleware\\EnsureFrontendRequestsAreStateful->handle()
#37 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#38 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then()
#39 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack()
#40 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute()
#41 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute()
#42 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch()
#43 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}()
#44 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#45 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#46 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#47 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#48 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#49 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#50 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#51 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle()
#52 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#53 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#54 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#55 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle()
#56 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#57 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#58 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#59 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle()
#60 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#61 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle()
#62 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#63 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then()
#64 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#65 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle()
#66 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('...')
#67 {main}

[previous exception] [object] (PDOException(code: 42S02): SQLSTATE[42S02]: Base table or view not found: 1146 Table 'barnatrasteros.alquilers' doesn't exist at C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:404)
[stacktrace]
#0 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(404): PDO->prepare()
#1 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(809): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}()
#2 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback()
#3 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(395): Illuminate\\Database\\Connection->run()
#4 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3131): Illuminate\\Database\\Connection->select()
#5 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3116): Illuminate\\Database\\Query\\Builder->runSelect()
#6 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3706): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#7 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3115): Illuminate\\Database\\Query\\Builder->onceWithColumns()
#8 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(872): Illuminate\\Database\\Query\\Builder->get()
#9 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(854): Illuminate\\Database\\Eloquent\\Builder->getModels()
#10 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\app\\Http\\Controllers\\Api\\DashboardController.php(70): Illuminate\\Database\\Eloquent\\Builder->get()
#11 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\Api\\DashboardController->pagosPendientes()
#12 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction()
#13 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch()
#14 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#15 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#16 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}()
#17 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#18 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle()
#19 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(63): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#20 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Auth\\Middleware\\Authenticate->handle()
#21 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(63): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#22 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Auth\\Middleware\\Authenticate->handle()
#23 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\sanctum\\src\\Http\\Middleware\\EnsureFrontendRequestsAreStateful.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#24 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Laravel\\Sanctum\\Http\\Middleware\\EnsureFrontendRequestsAreStateful->Laravel\\Sanctum\\Http\\Middleware\\{closure}()
#25 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#26 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle()
#27 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(120): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#28 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest()
#29 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle()
#30 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#31 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle()
#32 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#33 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle()
#34 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\sanctum\\src\\Http\\Middleware\\EnsureFrontendRequestsAreStateful.php(61): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#35 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(190): Laravel\\Sanctum\\Http\\Middleware\\EnsureFrontendRequestsAreStateful->Laravel\\Sanctum\\Http\\Middleware\\{closure}()
#36 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#37 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\sanctum\\src\\Http\\Middleware\\EnsureFrontendRequestsAreStateful.php(25): Illuminate\\Pipeline\\Pipeline->then()
#38 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Laravel\\Sanctum\\Http\\Middleware\\EnsureFrontendRequestsAreStateful->handle()
#39 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#40 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then()
#41 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack()
#42 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute()
#43 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute()
#44 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch()
#45 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}()
#46 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#47 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#48 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#49 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#50 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#51 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#52 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#53 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle()
#54 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#55 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#56 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#57 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle()
#58 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#59 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#60 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#61 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle()
#62 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#63 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle()
#64 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#65 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then()
#66 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#67 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle()
#68 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('...')
#69 {main}
"} 
[2025-07-09 11:14:45] local.DEBUG: SanctumCors  
[2025-07-09 11:14:45] local.DEBUG: http://localhost:3000  
[2025-07-09 11:47:12] local.ERROR: SQLSTATE[42S01]: Base table or view already exists: 1050 Table 'periodos_pisos' already exists (Connection: mysql, SQL: create table `periodos_pisos` (`id` bigint unsigned not null auto_increment primary key, `id_piso` bigint unsigned not null, `id_cliente` bigint unsigned not null, `date_from` date not null, `date_to` date null, `fianza` decimal(10, 2) not null default '0', `precio_mensual` decimal(10, 2) not null default '0', `observaciones` text null, `created_at` timestamp null, `updated_at` timestamp null) default character set utf8mb4 collate 'utf8mb4_unicode_ci') {"exception":"[object] (Illuminate\\Database\\QueryException(code: 42S01): SQLSTATE[42S01]: Base table or view already exists: 1050 Table 'periodos_pisos' already exists (Connection: mysql, SQL: create table `periodos_pisos` (`id` bigint unsigned not null auto_increment primary key, `id_piso` bigint unsigned not null, `id_cliente` bigint unsigned not null, `date_from` date not null, `date_to` date null, `fianza` decimal(10, 2) not null default '0', `precio_mensual` decimal(10, 2) not null default '0', `observaciones` text null, `created_at` timestamp null, `updated_at` timestamp null) default character set utf8mb4 collate 'utf8mb4_unicode_ci') at C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:822)
[stacktrace]
#0 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback()
#1 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(557): Illuminate\\Database\\Connection->run()
#2 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Blueprint.php(119): Illuminate\\Database\\Connection->statement()
#3 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(618): Illuminate\\Database\\Schema\\Blueprint->build()
#4 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(472): Illuminate\\Database\\Schema\\Builder->build()
#5 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(361): Illuminate\\Database\\Schema\\Builder->create()
#6 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\database\\migrations\\2023_01_01_000001_create_periodos_pisos_table.php(14): Illuminate\\Support\\Facades\\Facade::__callStatic()
#7 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(514): Illuminate\\Database\\Migrations\\Migration@anonymous->up()
#8 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(439): Illuminate\\Database\\Migrations\\Migrator->runMethod()
#9 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(448): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#10 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->runMigration()
#11 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\View\\Components\\Task.php(41): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#12 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(805): Illuminate\\Console\\View\\Components\\Task->render()
#13 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->write()
#14 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(210): Illuminate\\Database\\Migrations\\Migrator->runUp()
#15 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(137): Illuminate\\Database\\Migrations\\Migrator->runPending()
#16 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(116): Illuminate\\Database\\Migrations\\Migrator->run()
#17 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(665): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#18 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(109): Illuminate\\Database\\Migrations\\Migrator->usingConnection()
#19 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(88): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->runMigrations()
#20 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#21 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#22 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure()
#23 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#24 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call()
#25 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(209): Illuminate\\Container\\Container->call()
#26 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\symfony\\console\\Command\\Command.php(318): Illuminate\\Console\\Command->execute()
#27 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(178): Symfony\\Component\\Console\\Command\\Command->run()
#28 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\symfony\\console\\Application.php(1092): Illuminate\\Console\\Command->run()
#29 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\symfony\\console\\Application.php(341): Symfony\\Component\\Console\\Application->doRunCommand()
#30 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\symfony\\console\\Application.php(192): Symfony\\Component\\Console\\Application->doRun()
#31 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run()
#32 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle()
#33 {main}

[previous exception] [object] (PDOException(code: 42S01): SQLSTATE[42S01]: Base table or view already exists: 1050 Table 'periodos_pisos' already exists at C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:568)
[stacktrace]
#0 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(568): PDOStatement->execute()
#1 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(809): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}()
#2 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback()
#3 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(557): Illuminate\\Database\\Connection->run()
#4 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Blueprint.php(119): Illuminate\\Database\\Connection->statement()
#5 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(618): Illuminate\\Database\\Schema\\Blueprint->build()
#6 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(472): Illuminate\\Database\\Schema\\Builder->build()
#7 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(361): Illuminate\\Database\\Schema\\Builder->create()
#8 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\database\\migrations\\2023_01_01_000001_create_periodos_pisos_table.php(14): Illuminate\\Support\\Facades\\Facade::__callStatic()
#9 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(514): Illuminate\\Database\\Migrations\\Migration@anonymous->up()
#10 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(439): Illuminate\\Database\\Migrations\\Migrator->runMethod()
#11 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(448): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#12 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->runMigration()
#13 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\View\\Components\\Task.php(41): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#14 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(805): Illuminate\\Console\\View\\Components\\Task->render()
#15 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->write()
#16 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(210): Illuminate\\Database\\Migrations\\Migrator->runUp()
#17 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(137): Illuminate\\Database\\Migrations\\Migrator->runPending()
#18 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(116): Illuminate\\Database\\Migrations\\Migrator->run()
#19 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(665): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#20 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(109): Illuminate\\Database\\Migrations\\Migrator->usingConnection()
#21 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(88): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->runMigrations()
#22 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#23 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#24 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure()
#25 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#26 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call()
#27 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(209): Illuminate\\Container\\Container->call()
#28 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\symfony\\console\\Command\\Command.php(318): Illuminate\\Console\\Command->execute()
#29 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(178): Symfony\\Component\\Console\\Command\\Command->run()
#30 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\symfony\\console\\Application.php(1092): Illuminate\\Console\\Command->run()
#31 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\symfony\\console\\Application.php(341): Symfony\\Component\\Console\\Application->doRunCommand()
#32 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\symfony\\console\\Application.php(192): Symfony\\Component\\Console\\Application->doRun()
#33 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run()
#34 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle()
#35 {main}
"} 
[2025-07-09 11:49:12] local.DEBUG: SanctumCors  
[2025-07-09 11:49:12] local.DEBUG: http://localhost:3000  
[2025-07-09 11:51:55] local.DEBUG: SanctumCors  
[2025-07-09 11:51:55] local.DEBUG: http://localhost:3000  
[2025-07-09 11:51:56] local.DEBUG: SanctumCors  
[2025-07-09 11:51:56] local.DEBUG: http://localhost:3000  
[2025-07-09 11:51:58] local.DEBUG: SanctumCors  
[2025-07-09 11:51:58] local.DEBUG: http://localhost:3000  
[2025-07-09 11:51:58] local.DEBUG: SanctumCors  
[2025-07-09 11:51:58] local.DEBUG: http://localhost:3000  
[2025-07-09 11:52:11] local.DEBUG: SanctumCors  
[2025-07-09 11:52:11] local.DEBUG: http://localhost:3000  
[2025-07-09 11:52:12] local.DEBUG: SanctumCors  
[2025-07-09 11:52:12] local.DEBUG: http://localhost:3000  
