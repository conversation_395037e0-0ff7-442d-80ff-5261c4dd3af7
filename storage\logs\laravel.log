[2025-07-09 18:07:55] local.ERROR: Invalid route action: [App\Http\Controllers\Api\PisoController]. {"exception":"[object] (UnexpectedValueException(code: 0): Invalid route action: [App\\Http\\Controllers\\Api\\PisoController]. at C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\RouteAction.php:92)
[stacktrace]
#0 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\RouteAction.php(47): Illuminate\\Routing\\RouteAction::makeInvokable()
#1 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(197): Illuminate\\Routing\\RouteAction::parse()
#2 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(178): Illuminate\\Routing\\Route->parseAction()
#3 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(671): Illuminate\\Routing\\Route->__construct()
#4 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(562): Illuminate\\Routing\\Router->newRoute()
#5 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(542): Illuminate\\Routing\\Router->createRoute()
#6 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(160): Illuminate\\Routing\\Router->addRoute()
#7 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(361): Illuminate\\Routing\\Router->get()
#8 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\routes\\api.php(48): Illuminate\\Support\\Facades\\Facade::__callStatic()
#9 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(510): Illuminate\\Routing\\RouteFileRegistrar->{closure}()
#10 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(466): Illuminate\\Routing\\Router->loadRoutes()
#11 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\RouteRegistrar.php(206): Illuminate\\Routing\\Router->group()
#12 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\routes\\api.php(38): Illuminate\\Routing\\RouteRegistrar->group()
#13 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\RouteFileRegistrar.php(34): require('...')
#14 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(512): Illuminate\\Routing\\RouteFileRegistrar->register()
#15 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(466): Illuminate\\Routing\\Router->loadRoutes()
#16 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\RouteRegistrar.php(206): Illuminate\\Routing\\Router->group()
#17 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Configuration\\ApplicationBuilder.php(214): Illuminate\\Routing\\RouteRegistrar->group()
#18 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Foundation\\Configuration\\ApplicationBuilder->Illuminate\\Foundation\\Configuration\\{closure}()
#19 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#20 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(84): Illuminate\\Container\\Util::unwrapIfClosure()
#21 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#22 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call()
#23 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Support\\Providers\\RouteServiceProvider.php(162): Illuminate\\Container\\Container->call()
#24 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Support\\Providers\\RouteServiceProvider.php(59): Illuminate\\Foundation\\Support\\Providers\\RouteServiceProvider->loadRoutes()
#25 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Foundation\\Support\\Providers\\RouteServiceProvider->Illuminate\\Foundation\\Support\\Providers\\{closure}()
#26 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#27 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(84): Illuminate\\Container\\Util::unwrapIfClosure()
#28 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#29 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call()
#30 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\ServiceProvider.php(143): Illuminate\\Container\\Container->call()
#31 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1153): Illuminate\\Support\\ServiceProvider->callBootedCallbacks()
#32 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1131): Illuminate\\Foundation\\Application->bootProvider()
#33 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}()
#34 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1130): array_walk()
#35 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#36 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(341): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap()
#37 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(186): Illuminate\\Foundation\\Application->bootstrapWith()
#38 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(170): Illuminate\\Foundation\\Http\\Kernel->bootstrap()
#39 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#40 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle()
#41 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('...')
#42 {main}
"} 
[2025-07-10 08:34:08] local.ERROR: Invalid route action: [App\Http\Controllers\Api\PisoController]. {"exception":"[object] (UnexpectedValueException(code: 0): Invalid route action: [App\\Http\\Controllers\\Api\\PisoController]. at C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\RouteAction.php:92)
[stacktrace]
#0 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\RouteAction.php(47): Illuminate\\Routing\\RouteAction::makeInvokable()
#1 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(197): Illuminate\\Routing\\RouteAction::parse()
#2 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(178): Illuminate\\Routing\\Route->parseAction()
#3 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(671): Illuminate\\Routing\\Route->__construct()
#4 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(562): Illuminate\\Routing\\Router->newRoute()
#5 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(542): Illuminate\\Routing\\Router->createRoute()
#6 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(160): Illuminate\\Routing\\Router->addRoute()
#7 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(361): Illuminate\\Routing\\Router->get()
#8 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\routes\\api.php(48): Illuminate\\Support\\Facades\\Facade::__callStatic()
#9 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(510): Illuminate\\Routing\\RouteFileRegistrar->{closure}()
#10 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(466): Illuminate\\Routing\\Router->loadRoutes()
#11 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\RouteRegistrar.php(206): Illuminate\\Routing\\Router->group()
#12 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\routes\\api.php(38): Illuminate\\Routing\\RouteRegistrar->group()
#13 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\RouteFileRegistrar.php(34): require('...')
#14 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(512): Illuminate\\Routing\\RouteFileRegistrar->register()
#15 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(466): Illuminate\\Routing\\Router->loadRoutes()
#16 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\RouteRegistrar.php(206): Illuminate\\Routing\\Router->group()
#17 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Configuration\\ApplicationBuilder.php(214): Illuminate\\Routing\\RouteRegistrar->group()
#18 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Foundation\\Configuration\\ApplicationBuilder->Illuminate\\Foundation\\Configuration\\{closure}()
#19 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#20 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(84): Illuminate\\Container\\Util::unwrapIfClosure()
#21 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#22 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call()
#23 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Support\\Providers\\RouteServiceProvider.php(162): Illuminate\\Container\\Container->call()
#24 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Support\\Providers\\RouteServiceProvider.php(59): Illuminate\\Foundation\\Support\\Providers\\RouteServiceProvider->loadRoutes()
#25 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Foundation\\Support\\Providers\\RouteServiceProvider->Illuminate\\Foundation\\Support\\Providers\\{closure}()
#26 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#27 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(84): Illuminate\\Container\\Util::unwrapIfClosure()
#28 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#29 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call()
#30 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\ServiceProvider.php(143): Illuminate\\Container\\Container->call()
#31 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1153): Illuminate\\Support\\ServiceProvider->callBootedCallbacks()
#32 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1131): Illuminate\\Foundation\\Application->bootProvider()
#33 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}()
#34 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1130): array_walk()
#35 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#36 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(341): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap()
#37 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(473): Illuminate\\Foundation\\Application->bootstrapWith()
#38 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(195): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#39 C:\\Users\\<USER>\\Documents\\___CodeS\\BarnaTrasteros_1.0\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle()
#40 {main}
"} 
