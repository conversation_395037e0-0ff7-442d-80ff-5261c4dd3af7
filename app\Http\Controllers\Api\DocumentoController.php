<?php

namespace App\Http\Controllers\Api;

use App\Trastero;
use App\Models\Piso;
use App\Models\Documento;
use Illuminate\Http\Request;
use Illuminate\Validation\Rule;
use Illuminate\Http\JsonResponse;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;

class DocumentoController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request): JsonResponse
    {
        $query = Documento::with(['documentable', 'usuario']);

        // Filtros
        if ($request->has('tipo_documento')) {
            $query->where('tipo_documento', $request->tipo_documento);
        }

        if ($request->has('estado')) {
            $query->where('estado', $request->estado);
        }

        if ($request->has('documentable_type')) {
            $query->where('documentable_type', $request->documentable_type);
        }

        if ($request->has('documentable_id')) {
            $query->where('documentable_id', $request->documentable_id);
        }

        if ($request->has('fecha_desde')) {
            $query->where('fecha_documento', '>=', $request->fecha_desde);
        }

        if ($request->has('fecha_hasta')) {
            $query->where('fecha_documento', '<=', $request->fecha_hasta);
        }

        $documentos = $query->orderBy('fecha_documento', 'desc')
                           ->paginate($request->get('per_page', 15));

        return response()->json($documentos);
    }

    /**
     * Subir un documento para un trastero o piso
     */
    public function store(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'nombre' => 'required|string|max:255',
            'descripcion' => 'nullable|string',
            'archivo' => 'required|file|max:10240', // 10MB max
            'tipo' => 'required|string|in:trastero,piso',
            'id' => 'required|integer'
        ]);

        // Determinar el modelo relacionado
        if ($validated['tipo'] === 'trastero') {
            $model = Trastero::findOrFail($validated['id']);
        } else {
            $model = Piso::findOrFail($validated['id']);
        }

        // Guardar el archivo
        $path = $request->file('archivo')->store('documentos/' . $validated['tipo'] . '/' . $validated['id']);
        
        // Crear el documento
        $documento = new Documento([
            'nombre' => $validated['nombre'],
            'descripcion' => $validated['descripcion'],
            'ruta_archivo' => $path,
            'tipo_archivo' => $request->file('archivo')->getClientOriginalExtension(),
            'subido_por' => Auth::id()
        ]);
        
        // Asociar al modelo correspondiente
        $model->documentos()->save($documento);

        return response()->json($documento->load('usuario'), 201);
    }

    /**
     * Display the specified resource.
     */
    public function show(Documento $documento): JsonResponse
    {
        return response()->json($documento->load(['documentable', 'usuario']));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Documento $documento): JsonResponse
    {
        $validated = $request->validate([
            'nombre' => 'sometimes|string|max:255',
            'tipo_documento' => 'sometimes|string|max:255',
            'fecha_documento' => 'sometimes|date',
            'importe' => 'nullable|numeric|min:0',
            'descripcion' => 'nullable|string',
            'estado' => ['sometimes', Rule::in(['pendiente', 'procesado', 'archivado'])],
        ]);

        $documento->update($validated);

        return response()->json($documento->load(['documentable', 'usuario']));
    }

    /**
     * Descargar un documento
     */
    public function download(Documento $documento)
    {
        return Storage::download($documento->ruta_archivo, $documento->nombre . '.' . $documento->tipo_archivo);
    }

    /**
     * Eliminar un documento
     */
    public function destroy(Documento $documento): JsonResponse
    {
        // Eliminar el archivo
        Storage::delete($documento->ruta_archivo);
        
        // Eliminar el registro
        $documento->delete();

        return response()->json(['message' => 'Documento eliminado correctamente']);
    }

    /**
     * Get documents by type
     */
    public function porTipo(string $tipo): JsonResponse
    {
        $documentos = Documento::porTipo($tipo)
                              ->with(['documentable', 'usuario'])
                              ->orderBy('fecha_documento', 'desc')
                              ->get();

        return response()->json($documentos);
    }

    /**
     * Get pending documents
     */
    public function pendientes(): JsonResponse
    {
        $documentos = Documento::pendientes()
                              ->with(['documentable', 'usuario'])
                              ->orderBy('fecha_documento', 'desc')
                              ->get();

        return response()->json($documentos);
    }

    /**
     * Upload múltiple de documentos
     */
    public function uploadMultiple(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'archivos.*' => 'required|file|max:10240', // 10MB max por archivo
            'tipo' => 'required|string|in:trastero,piso,cliente,gasto',
            'id' => 'required|integer',
            'tipo_documento' => 'required|string|max:255',
            'descripcion' => 'nullable|string'
        ]);

        // Verificar que el modelo existe
        $modelClass = match($validated['tipo']) {
            'trastero' => \App\Trastero::class,
            'piso' => \App\Models\Piso::class,
            'cliente' => \App\Cliente::class,
            'gasto' => \App\Models\GastoEdificio::class,
        };

        $model = $modelClass::findOrFail($validated['id']);
        $documentos = [];

        foreach ($request->file('archivos') as $archivo) {
            // Guardar el archivo
            $path = $archivo->store('documentos/' . $validated['tipo'] . '/' . $validated['id']);

            // Crear el documento
            $documento = new Documento([
                'nombre' => pathinfo($archivo->getClientOriginalName(), PATHINFO_FILENAME),
                'tipo_documento' => $validated['tipo_documento'],
                'descripcion' => $validated['descripcion'],
                'ruta_archivo' => $path,
                'tipo_archivo' => $archivo->getClientOriginalExtension(),
                'fecha_documento' => now(),
                'estado' => 'procesado',
                'subido_por' => Auth::id()
            ]);

            // Asociar al modelo correspondiente
            $model->documentos()->save($documento);
            $documentos[] = $documento->load('usuario');
        }

        return response()->json([
            'message' => 'Documentos subidos correctamente',
            'documentos' => $documentos
        ], 201);
    }

    /**
     * Obtener versiones de un documento
     */
    public function versiones(Documento $documento): JsonResponse
    {
        $versiones = Documento::where('documento_padre_id', $documento->id)
                             ->orWhere('id', $documento->id)
                             ->with(['usuario'])
                             ->orderBy('created_at', 'desc')
                             ->get();

        return response()->json($versiones);
    }

    /**
     * Crear nueva versión de un documento
     */
    public function nuevaVersion(Request $request, Documento $documento): JsonResponse
    {
        $validated = $request->validate([
            'archivo' => 'required|file|max:10240',
            'descripcion' => 'nullable|string',
            'motivo_cambio' => 'required|string|max:255'
        ]);

        // Guardar el nuevo archivo
        $path = $request->file('archivo')->store('documentos/versiones/' . $documento->id);

        // Crear nueva versión
        $nuevaVersion = new Documento([
            'nombre' => $documento->nombre . ' (v' . (time()) . ')',
            'tipo_documento' => $documento->tipo_documento,
            'descripcion' => $validated['descripcion'] ?? $documento->descripcion,
            'ruta_archivo' => $path,
            'tipo_archivo' => $request->file('archivo')->getClientOriginalExtension(),
            'fecha_documento' => now(),
            'estado' => 'procesado',
            'documento_padre_id' => $documento->id,
            'motivo_cambio' => $validated['motivo_cambio'],
            'subido_por' => Auth::id()
        ]);

        // Asociar al mismo modelo que el documento original
        $documento->documentable->documentos()->save($nuevaVersion);

        return response()->json($nuevaVersion->load('usuario'), 201);
    }

    /**
     * Cambiar estado de un documento
     */
    public function cambiarEstado(Request $request, Documento $documento): JsonResponse
    {
        $validated = $request->validate([
            'estado' => ['required', Rule::in(['pendiente', 'procesado', 'archivado'])],
            'motivo' => 'nullable|string|max:255'
        ]);

        $documento->update($validated);

        return response()->json([
            'message' => 'Estado del documento actualizado correctamente',
            'documento' => $documento->load(['documentable', 'usuario'])
        ]);
    }

    /**
     * Búsqueda avanzada de documentos
     */
    public function buscar(Request $request): JsonResponse
    {
        $query = Documento::with(['documentable', 'usuario']);

        // Filtros de búsqueda
        if ($request->filled('q')) {
            $searchTerm = $request->q;
            $query->where(function ($q) use ($searchTerm) {
                $q->where('nombre', 'like', "%{$searchTerm}%")
                  ->orWhere('tipo_documento', 'like', "%{$searchTerm}%")
                  ->orWhere('descripcion', 'like', "%{$searchTerm}%");
            });
        }

        if ($request->filled('tipo_documento')) {
            $query->where('tipo_documento', $request->tipo_documento);
        }

        if ($request->filled('estado')) {
            $query->where('estado', $request->estado);
        }

        if ($request->filled('documentable_type')) {
            $query->where('documentable_type', $request->documentable_type);
        }

        if ($request->filled('fecha_desde')) {
            $query->where('fecha_documento', '>=', $request->fecha_desde);
        }

        if ($request->filled('fecha_hasta')) {
            $query->where('fecha_documento', '<=', $request->fecha_hasta);
        }

        if ($request->filled('subido_por')) {
            $query->where('subido_por', $request->subido_por);
        }

        // Ordenamiento
        $sortBy = $request->get('sort_by', 'fecha_documento');
        $sortOrder = $request->get('sort_order', 'desc');
        $query->orderBy($sortBy, $sortOrder);

        $documentos = $query->paginate($request->get('per_page', 15));

        return response()->json($documentos);
    }
}


