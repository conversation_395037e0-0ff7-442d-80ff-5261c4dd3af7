<?php

namespace App\Http\Controllers\Api;

use App\Trastero;
use App\Models\Piso;
use App\Models\Documento;
use Illuminate\Http\Request;
use Illuminate\Validation\Rule;
use Illuminate\Http\JsonResponse;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;

class DocumentoController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request): JsonResponse
    {
        $query = Documento::with(['documentable', 'usuario']);

        // Filtros
        if ($request->has('tipo_documento')) {
            $query->where('tipo_documento', $request->tipo_documento);
        }

        if ($request->has('estado')) {
            $query->where('estado', $request->estado);
        }

        if ($request->has('documentable_type')) {
            $query->where('documentable_type', $request->documentable_type);
        }

        if ($request->has('documentable_id')) {
            $query->where('documentable_id', $request->documentable_id);
        }

        if ($request->has('fecha_desde')) {
            $query->where('fecha_documento', '>=', $request->fecha_desde);
        }

        if ($request->has('fecha_hasta')) {
            $query->where('fecha_documento', '<=', $request->fecha_hasta);
        }

        $documentos = $query->orderBy('fecha_documento', 'desc')
                           ->paginate($request->get('per_page', 15));

        return response()->json($documentos);
    }

    /**
     * Subir un documento para un trastero o piso
     */
    public function store(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'nombre' => 'required|string|max:255',
            'descripcion' => 'nullable|string',
            'archivo' => 'required|file|max:10240', // 10MB max
            'tipo' => 'required|string|in:trastero,piso',
            'id' => 'required|integer'
        ]);

        // Determinar el modelo relacionado
        if ($validated['tipo'] === 'trastero') {
            $model = Trastero::findOrFail($validated['id']);
        } else {
            $model = Piso::findOrFail($validated['id']);
        }

        // Guardar el archivo
        $path = $request->file('archivo')->store('documentos/' . $validated['tipo'] . '/' . $validated['id']);
        
        // Crear el documento
        $documento = new Documento([
            'nombre' => $validated['nombre'],
            'descripcion' => $validated['descripcion'],
            'ruta_archivo' => $path,
            'tipo_archivo' => $request->file('archivo')->getClientOriginalExtension(),
            'subido_por' => Auth::id()
        ]);
        
        // Asociar al modelo correspondiente
        $model->documentos()->save($documento);

        return response()->json($documento->load('usuario'), 201);
    }

    /**
     * Display the specified resource.
     */
    public function show(Documento $documento): JsonResponse
    {
        return response()->json($documento->load(['documentable', 'usuario']));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Documento $documento): JsonResponse
    {
        $validated = $request->validate([
            'nombre' => 'sometimes|string|max:255',
            'tipo_documento' => 'sometimes|string|max:255',
            'fecha_documento' => 'sometimes|date',
            'importe' => 'nullable|numeric|min:0',
            'descripcion' => 'nullable|string',
            'estado' => ['sometimes', Rule::in(['pendiente', 'procesado', 'archivado'])],
        ]);

        $documento->update($validated);

        return response()->json($documento->load(['documentable', 'usuario']));
    }

    /**
     * Descargar un documento
     */
    public function download(Documento $documento)
    {
        return Storage::download($documento->ruta_archivo, $documento->nombre . '.' . $documento->tipo_archivo);
    }

    /**
     * Eliminar un documento
     */
    public function destroy(Documento $documento): JsonResponse
    {
        // Eliminar el archivo
        Storage::delete($documento->ruta_archivo);
        
        // Eliminar el registro
        $documento->delete();

        return response()->json(['message' => 'Documento eliminado correctamente']);
    }

    /**
     * Get documents by type
     */
    public function porTipo(string $tipo): JsonResponse
    {
        $documentos = Documento::porTipo($tipo)
                              ->with(['documentable', 'usuario'])
                              ->orderBy('fecha_documento', 'desc')
                              ->get();

        return response()->json($documentos);
    }

    /**
     * Get pending documents
     */
    public function pendientes(): JsonResponse
    {
        $documentos = Documento::pendientes()
                              ->with(['documentable', 'usuario'])
                              ->orderBy('fecha_documento', 'desc')
                              ->get();

        return response()->json($documentos);
    }
}


