<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Alquiler;
use App\Cliente;
use App\Periodo;
use App\Models\PeriodoPiso;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Carbon\Carbon;

class AlquilerController extends Controller
{
    /**
     * Listar alquileres con filtros
     */
    public function index(Request $request): JsonResponse
    {
        $query = Alquiler::with(['cliente', 'alquilable']);

        // Filtros
        if ($request->has('cliente_id')) {
            $query->where('id_cliente', $request->cliente_id);
        }

        if ($request->has('estado_pago')) {
            $query->where('estado_pago', $request->estado_pago);
        }

        if ($request->has('mes')) {
            $query->where('M', $request->mes);
        }

        if ($request->has('año') || $request->has('anio')) {
            $anio = $request->año ?? $request->anio;
            $query->where('A', $anio);
        }

        if ($request->has('vencidos') && $request->boolean('vencidos')) {
            $query->where('fecha_vencimiento', '<', Carbon::now())
                  ->where('estado_pago', 'pendiente');
        }

        $alquileres = $query->paginate($request->get('per_page', 15));

        return response()->json($alquileres);
    }

    /**
     * Registrar un nuevo pago de alquiler
     */
    public function store(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'cliente_id' => 'required|exists:clientes,id',
            'tipo' => 'required|in:trastero,piso',
            'periodo_id' => 'required|integer',
            'mes' => 'required|integer|min:1|max:12',
            'año' => 'required|integer|min:2000|max:2100',
            'valor' => 'required|numeric|min:0',
            'valor_pago' => 'nullable|numeric|min:0',
            'estado_pago' => 'required|in:pendiente,pagado,parcial',
            'fecha_vencimiento' => 'required|date',
            'recargo_mora' => 'nullable|numeric|min:0',
            'observaciones' => 'nullable|string'
        ]);

        // Determinar el modelo relacionado
        if ($validated['tipo'] === 'trastero') {
            $periodo = Periodo::findOrFail($validated['periodo_id']);
            $alquilableType = Periodo::class;
        } else {
            $periodo = PeriodoPiso::findOrFail($validated['periodo_id']);
            $alquilableType = PeriodoPiso::class;
        }

        // Crear el alquiler
        $alquiler = new Alquiler([
            'M' => $validated['mes'],
            'A' => $validated['año'],
            'id_cliente' => $validated['cliente_id'],
            'id_periodo' => $validated['periodo_id'],
            'valor' => $validated['valor'],
            'valor_pago' => $validated['valor_pago'] ?? 0,
            'estado_pago' => $validated['estado_pago'],
            'fecha_vencimiento' => $validated['fecha_vencimiento'],
            'recargo_mora' => $validated['recargo_mora'] ?? 0,
            'observaciones' => $validated['observaciones'] ?? null
        ]);

        // Asociar al modelo correspondiente
        $alquiler->alquilable_id = $validated['periodo_id'];
        $alquiler->alquilable_type = $alquilableType;
        $alquiler->save();

        return response()->json($alquiler->load(['cliente', 'alquilable']), 201);
    }

    /**
     * Actualizar un pago de alquiler
     */
    public function update(Request $request, Alquiler $alquiler): JsonResponse
    {
        $validated = $request->validate([
            'valor' => 'sometimes|numeric|min:0',
            'valor_pago' => 'sometimes|numeric|min:0',
            'estado_pago' => 'sometimes|in:pendiente,pagado,parcial',
            'fecha_vencimiento' => 'sometimes|date',
            'recargo_mora' => 'sometimes|numeric|min:0',
            'observaciones' => 'nullable|string'
        ]);

        $alquiler->update($validated);

        // Si se ha pagado completamente, actualizar estado
        if ($alquiler->valor_pago >= $alquiler->valor && $alquiler->estado_pago !== 'pagado') {
            $alquiler->update(['estado_pago' => 'pagado']);
        }

        return response()->json($alquiler->load(['cliente', 'alquilable']));
    }

    /**
     * Eliminar un pago de alquiler
     */
    public function destroy(Alquiler $alquiler): JsonResponse
    {
        $alquiler->delete();
        return response()->json(['message' => 'Pago eliminado correctamente']);
    }

    /**
     * Listar pagos pendientes
     */
    public function pendientes(): JsonResponse
    {
        $alquileres = Alquiler::where('estado_pago', 'pendiente')
                             ->orWhere('estado_pago', 'parcial')
                             ->with(['cliente', 'alquilable'])
                             ->get();
        
        return response()->json($alquileres);
    }

    /**
     * Registrar pago de un alquiler
     */
    public function registrarPago(Request $request, Alquiler $alquiler): JsonResponse
    {
        $validated = $request->validate([
            'valor_pago' => 'required|numeric|min:0',
            'observaciones' => 'nullable|string'
        ]);

        $nuevoValorPago = $alquiler->valor_pago + $validated['valor_pago'];
        $nuevoEstado = 'pendiente';
        
        if ($nuevoValorPago >= $alquiler->valor) {
            $nuevoEstado = 'pagado';
        } elseif ($nuevoValorPago > 0) {
            $nuevoEstado = 'parcial';
        }

        $alquiler->update([
            'valor_pago' => $nuevoValorPago,
            'estado_pago' => $nuevoEstado,
            'observaciones' => $validated['observaciones'] ?? $alquiler->observaciones
        ]);

        return response()->json($alquiler->load(['cliente', 'alquilable']));
    }

    /**
     * Listar pagos vencidos
     */
    public function vencidos(): JsonResponse
    {
        $alquileres = Alquiler::where('estado_pago', 'pendiente')
                             ->where('fecha_vencimiento', '<', Carbon::now())
                             ->with(['cliente', 'alquilable'])
                             ->orderBy('fecha_vencimiento', 'asc')
                             ->get();

        return response()->json($alquileres);
    }

    /**
     * Listar pagos al día
     */
    public function alDia(): JsonResponse
    {
        $alquileres = Alquiler::where('estado_pago', 'pagado')
                             ->with(['cliente', 'alquilable'])
                             ->orderBy('A', 'desc')
                             ->orderBy('M', 'desc')
                             ->get();

        return response()->json($alquileres);
    }

    /**
     * Listar pagos parciales
     */
    public function parciales(): JsonResponse
    {
        $alquileres = Alquiler::where('estado_pago', 'parcial')
                             ->with(['cliente', 'alquilable'])
                             ->orderBy('fecha_vencimiento', 'asc')
                             ->get();

        return response()->json($alquileres);
    }

    /**
     * Actualizar estados de pago masivamente
     */
    public function actualizarEstadosPago(): JsonResponse
    {
        $actualizados = 0;

        // Actualizar pagos vencidos
        $vencidos = Alquiler::where('estado_pago', 'pendiente')
                           ->where('fecha_vencimiento', '<', Carbon::now())
                           ->update(['estado_pago' => 'vencido']);

        $actualizados += $vencidos;

        return response()->json([
            'message' => 'Estados de pago actualizados correctamente',
            'actualizados' => $actualizados
        ]);
    }

    /**
     * Búsqueda avanzada de alquileres
     */
    public function buscar(Request $request): JsonResponse
    {
        $query = Alquiler::with(['cliente', 'alquilable']);

        // Filtros de búsqueda
        if ($request->filled('q')) {
            $searchTerm = $request->q;
            $query->whereHas('cliente', function($q) use ($searchTerm) {
                $q->where('nombre', 'like', "%{$searchTerm}%")
                  ->orWhere('apellido', 'like', "%{$searchTerm}%")
                  ->orWhere('documento', 'like', "%{$searchTerm}%");
            });
        }

        if ($request->filled('estado_pago')) {
            $query->where('estado_pago', $request->estado_pago);
        }

        if ($request->filled('mes')) {
            $query->where('M', $request->mes);
        }

        if ($request->filled('año')) {
            $query->where('A', $request->año);
        }

        if ($request->filled('fecha_vencimiento_desde')) {
            $query->where('fecha_vencimiento', '>=', $request->fecha_vencimiento_desde);
        }

        if ($request->filled('fecha_vencimiento_hasta')) {
            $query->where('fecha_vencimiento', '<=', $request->fecha_vencimiento_hasta);
        }

        if ($request->filled('valor_min')) {
            $query->where('valor', '>=', $request->valor_min);
        }

        if ($request->filled('valor_max')) {
            $query->where('valor', '<=', $request->valor_max);
        }

        // Ordenamiento
        $sortBy = $request->get('sort_by', 'fecha_vencimiento');
        $sortOrder = $request->get('sort_order', 'desc');
        $query->orderBy($sortBy, $sortOrder);

        $alquileres = $query->paginate($request->get('per_page', 15));

        return response()->json($alquileres);
    }

    /**
     * Historial de pagos de un alquiler
     */
    public function historialPagos(Alquiler $alquiler): JsonResponse
    {
        // Por ahora devolvemos el alquiler con su información
        // En una implementación más completa, esto incluiría un historial de transacciones
        $historial = [
            'alquiler' => $alquiler->load(['cliente', 'alquilable']),
            'transacciones' => [
                [
                    'fecha' => $alquiler->updated_at,
                    'tipo' => 'pago',
                    'monto' => $alquiler->valor_pago,
                    'estado' => $alquiler->estado_pago,
                    'observaciones' => $alquiler->observaciones
                ]
            ]
        ];

        return response()->json($historial);
    }

    /**
     * Generar factura para un alquiler
     */
    public function generarFactura(Alquiler $alquiler): JsonResponse
    {
        // Placeholder para funcionalidad de generación de facturas
        return response()->json([
            'message' => 'Factura generada correctamente',
            'factura_url' => null, // URL del PDF generado
            'numero_factura' => 'FAC-' . $alquiler->id . '-' . date('Y'),
            'alquiler' => $alquiler->load(['cliente', 'alquilable'])
        ]);
    }

    /**
     * Enviar recordatorio de pago
     */
    public function enviarRecordatorio(Alquiler $alquiler): JsonResponse
    {
        // Placeholder para funcionalidad de envío de recordatorios
        return response()->json([
            'message' => 'Recordatorio enviado correctamente',
            'enviado_a' => $alquiler->cliente->nombre . ' ' . $alquiler->cliente->apellido,
            'fecha_envio' => Carbon::now()
        ]);
    }

    /**
     * Estadísticas de pagos
     */
    public function estadisticasPagos(): JsonResponse
    {
        $stats = [
            'total_alquileres' => Alquiler::count(),
            'pagados' => Alquiler::where('estado_pago', 'pagado')->count(),
            'pendientes' => Alquiler::where('estado_pago', 'pendiente')->count(),
            'parciales' => Alquiler::where('estado_pago', 'parcial')->count(),
            'vencidos' => Alquiler::where('estado_pago', 'pendiente')
                                 ->where('fecha_vencimiento', '<', Carbon::now())
                                 ->count(),
            'total_facturado' => Alquiler::sum('valor'),
            'total_cobrado' => Alquiler::sum('valor_pago'),
            'total_pendiente' => Alquiler::where('estado_pago', '!=', 'pagado')->sum('valor') -
                               Alquiler::where('estado_pago', '!=', 'pagado')->sum('valor_pago'),
            'porcentaje_cobro' => Alquiler::sum('valor') > 0 ?
                                round((Alquiler::sum('valor_pago') / Alquiler::sum('valor')) * 100, 2) : 0
        ];

        return response()->json($stats);
    }
}



