<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Alquiler;
use App\Cliente;
use App\Periodo;
use App\Models\PeriodoPiso;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Carbon\Carbon;

class AlquilerController extends Controller
{
    /**
     * Listar alquileres con filtros
     */
    public function index(Request $request): JsonResponse
    {
        $query = Alquiler::with(['cliente', 'alquilable']);

        // Filtros
        if ($request->has('cliente_id')) {
            $query->where('id_cliente', $request->cliente_id);
        }

        if ($request->has('estado_pago')) {
            $query->where('estado_pago', $request->estado_pago);
        }

        if ($request->has('mes')) {
            $query->where('M', $request->mes);
        }

        if ($request->has('año') || $request->has('anio')) {
            $anio = $request->año ?? $request->anio;
            $query->where('A', $anio);
        }

        if ($request->has('vencidos') && $request->boolean('vencidos')) {
            $query->where('fecha_vencimiento', '<', Carbon::now())
                  ->where('estado_pago', 'pendiente');
        }

        $alquileres = $query->paginate($request->get('per_page', 15));

        return response()->json($alquileres);
    }

    /**
     * Registrar un nuevo pago de alquiler
     */
    public function store(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'cliente_id' => 'required|exists:clientes,id',
            'tipo' => 'required|in:trastero,piso',
            'periodo_id' => 'required|integer',
            'mes' => 'required|integer|min:1|max:12',
            'año' => 'required|integer|min:2000|max:2100',
            'valor' => 'required|numeric|min:0',
            'valor_pago' => 'nullable|numeric|min:0',
            'estado_pago' => 'required|in:pendiente,pagado,parcial',
            'fecha_vencimiento' => 'required|date',
            'recargo_mora' => 'nullable|numeric|min:0',
            'observaciones' => 'nullable|string'
        ]);

        // Determinar el modelo relacionado
        if ($validated['tipo'] === 'trastero') {
            $periodo = Periodo::findOrFail($validated['periodo_id']);
            $alquilableType = Periodo::class;
        } else {
            $periodo = PeriodoPiso::findOrFail($validated['periodo_id']);
            $alquilableType = PeriodoPiso::class;
        }

        // Crear el alquiler
        $alquiler = new Alquiler([
            'M' => $validated['mes'],
            'A' => $validated['año'],
            'id_cliente' => $validated['cliente_id'],
            'id_periodo' => $validated['periodo_id'],
            'valor' => $validated['valor'],
            'valor_pago' => $validated['valor_pago'] ?? 0,
            'estado_pago' => $validated['estado_pago'],
            'fecha_vencimiento' => $validated['fecha_vencimiento'],
            'recargo_mora' => $validated['recargo_mora'] ?? 0,
            'observaciones' => $validated['observaciones'] ?? null
        ]);

        // Asociar al modelo correspondiente
        $alquiler->alquilable_id = $validated['periodo_id'];
        $alquiler->alquilable_type = $alquilableType;
        $alquiler->save();

        return response()->json($alquiler->load(['cliente', 'alquilable']), 201);
    }

    /**
     * Actualizar un pago de alquiler
     */
    public function update(Request $request, Alquiler $alquiler): JsonResponse
    {
        $validated = $request->validate([
            'valor' => 'sometimes|numeric|min:0',
            'valor_pago' => 'sometimes|numeric|min:0',
            'estado_pago' => 'sometimes|in:pendiente,pagado,parcial',
            'fecha_vencimiento' => 'sometimes|date',
            'recargo_mora' => 'sometimes|numeric|min:0',
            'observaciones' => 'nullable|string'
        ]);

        $alquiler->update($validated);

        // Si se ha pagado completamente, actualizar estado
        if ($alquiler->valor_pago >= $alquiler->valor && $alquiler->estado_pago !== 'pagado') {
            $alquiler->update(['estado_pago' => 'pagado']);
        }

        return response()->json($alquiler->load(['cliente', 'alquilable']));
    }

    /**
     * Eliminar un pago de alquiler
     */
    public function destroy(Alquiler $alquiler): JsonResponse
    {
        $alquiler->delete();
        return response()->json(['message' => 'Pago eliminado correctamente']);
    }

    /**
     * Listar pagos pendientes
     */
    public function pendientes(): JsonResponse
    {
        $alquileres = Alquiler::where('estado_pago', 'pendiente')
                             ->orWhere('estado_pago', 'parcial')
                             ->with(['cliente', 'alquilable'])
                             ->get();
        
        return response()->json($alquileres);
    }

    /**
     * Registrar pago de un alquiler
     */
    public function registrarPago(Request $request, Alquiler $alquiler): JsonResponse
    {
        $validated = $request->validate([
            'valor_pago' => 'required|numeric|min:0',
            'observaciones' => 'nullable|string'
        ]);

        $nuevoValorPago = $alquiler->valor_pago + $validated['valor_pago'];
        $nuevoEstado = 'pendiente';
        
        if ($nuevoValorPago >= $alquiler->valor) {
            $nuevoEstado = 'pagado';
        } elseif ($nuevoValorPago > 0) {
            $nuevoEstado = 'parcial';
        }

        $alquiler->update([
            'valor_pago' => $nuevoValorPago,
            'estado_pago' => $nuevoEstado,
            'observaciones' => $validated['observaciones'] ?? $alquiler->observaciones
        ]);

        return response()->json($alquiler->load(['cliente', 'alquilable']));
    }
}



