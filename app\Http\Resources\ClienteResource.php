<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class ClienteResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'nombre' => $this->nombre,
            'apellido' => $this->apellido,
            'nombre_completo' => $this->nombre . ' ' . $this->apellido,
            'documento' => $this->documento,
            'telefono' => $this->telefono,
            'direccion' => $this->direccion,
            'direccion2' => $this->direccion2,
            'cp' => $this->cp,
            'ciudad' => $this->ciudad,
            'born' => $this->born,
            'descripcion' => $this->descripcion,
            'status' => $this->status,
            'posible_cliente' => $this->posible_cliente,
            'total_pago' => $this->total_pago,
            'fianza' => $this->fianza,
            'vencimiento' => $this->vencimiento,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
        ];
    }
}
