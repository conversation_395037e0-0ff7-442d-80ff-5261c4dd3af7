<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class TrasteroResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'numero' => $this->num,
            'titulo' => $this->titulo,
            'descripcion' => $this->descripcion,
            'precio' => $this->price,
            'ocupado' => $this->ocupado,
            'tipo_trastero' => new TipoTrasteroResource($this->whenLoaded('tipoTrastero')),
            'periodo_activo' => new PeriodoResource($this->whenLoaded('periodoActivo')),
            'documentos' => DocumentoResource::collection($this->whenLoaded('documentos')),
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
        ];
    }
}
