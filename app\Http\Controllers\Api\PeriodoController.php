<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Periodo;
use App\Trastero;
use App\Cliente;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Carbon\Carbon;

class PeriodoController extends Controller
{
    /**
     * Listar periodos de alquiler de trasteros
     */
    public function index(Request $request): JsonResponse
    {
        $query = Periodo::with(['trastero', 'cliente']);

        // Filtros
        if ($request->has('trastero_id')) {
            $query->where('id_trastero', $request->trastero_id);
        }

        if ($request->has('cliente_id')) {
            $query->where('id_cliente', $request->cliente_id);
        }

        if ($request->has('activos') && $request->boolean('activos')) {
            $query->whereNull('date_to');
        }

        if ($request->has('finalizados') && $request->boolean('finalizados')) {
            $query->whereNotNull('date_to');
        }

        $periodos = $query->paginate($request->get('per_page', 15));

        return response()->json($periodos);
    }

    /**
     * Crear un nuevo periodo de alquiler de trastero
     */
    public function store(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'id_trastero' => 'required|exists:trasteros,id',
            'id_cliente' => 'required|exists:clientes,id',
            'date_from' => 'required|date',
            'date_to' => 'nullable|date|after:date_from',
            'fianza' => 'required|numeric|min:0'
        ]);

        // Verificar que el trastero esté disponible
        $trastero = Trastero::findOrFail($validated['id_trastero']);
        
        if ($trastero->ocupado) {
            return response()->json([
                'message' => 'El trastero no está disponible para alquilar'
            ], 422);
        }

        // Crear el periodo
        $periodo = Periodo::create($validated);

        // Actualizar estado del trastero
        $trastero->update(['ocupado' => true]);

        // Actualizar cliente si es posible cliente
        $cliente = Cliente::findOrFail($validated['id_cliente']);
        if ($cliente->posible_cliente) {
            $cliente->update(['posible_cliente' => false, 'status' => true]);
        }

        return response()->json($periodo->load(['trastero', 'cliente']), 201);
    }

    /**
     * Mostrar un periodo específico
     */
    public function show(Periodo $periodo): JsonResponse
    {
        return response()->json($periodo->load(['trastero', 'cliente', 'alquileres']));
    }

    /**
     * Actualizar un periodo de alquiler
     */
    public function update(Request $request, Periodo $periodo): JsonResponse
    {
        $validated = $request->validate([
            'date_from' => 'sometimes|date',
            'date_to' => 'nullable|date|after:date_from',
            'fianza' => 'sometimes|numeric|min:0'
        ]);

        $periodo->update($validated);

        // Si se finaliza el periodo, liberar el trastero
        if (isset($validated['date_to']) && $validated['date_to']) {
            $trastero = Trastero::findOrFail($periodo->id_trastero);
            $trastero->update(['ocupado' => false]);
        }

        return response()->json($periodo->load(['trastero', 'cliente']));
    }

    /**
     * Finalizar un periodo de alquiler
     */
    public function finalizar(Request $request, Periodo $periodo): JsonResponse
    {
        $validated = $request->validate([
            'date_to' => 'required|date',
            'observaciones' => 'nullable|string'
        ]);

        $periodo->update([
            'date_to' => $validated['date_to'],
            'observaciones' => $validated['observaciones'] ?? null
        ]);

        // Liberar el trastero
        $trastero = Trastero::findOrFail($periodo->id_trastero);
        $trastero->update(['ocupado' => false]);

        return response()->json($periodo->load(['trastero', 'cliente']));
    }

    /**
     * Eliminar un periodo de alquiler
     */
    public function destroy(Periodo $periodo): JsonResponse
    {
        // Verificar que no tenga alquileres asociados
        if ($periodo->alquileres()->count() > 0) {
            return response()->json([
                'message' => 'No se puede eliminar un periodo con alquileres asociados'
            ], 422);
        }

        // Liberar el trastero si el periodo estaba activo
        if (!$periodo->date_to) {
            $trastero = Trastero::findOrFail($periodo->id_trastero);
            $trastero->update(['ocupado' => false]);
        }

        $periodo->delete();

        return response()->json(['message' => 'Periodo eliminado correctamente']);
    }
}