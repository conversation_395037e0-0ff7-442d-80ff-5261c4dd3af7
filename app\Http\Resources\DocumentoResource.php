<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class DocumentoResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'nombre' => $this->nombre,
            'tipo_documento' => $this->tipo_documento,
            'archivo_path' => $this->archivo_path,
            'ruta_archivo' => $this->ruta_archivo,
            'archivo_nombre_original' => $this->archivo_nombre_original,
            'mime_type' => $this->mime_type,
            'tipo_archivo' => $this->tipo_archivo,
            'tamano_archivo' => $this->tamano_archivo,
            'tamano_formateado' => $this->tamano_formateado,
            'fecha_documento' => $this->fecha_documento,
            'importe' => $this->importe,
            'descripcion' => $this->descripcion,
            'estado' => $this->estado,
            'version' => $this->version,
            'motivo_cambio' => $this->motivo_cambio,
            'documento_padre_id' => $this->documento_padre_id,
            'subido_por' => new UserResource($this->whenLoaded('usuario')),
            'url' => $this->url,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
        ];
    }
}
