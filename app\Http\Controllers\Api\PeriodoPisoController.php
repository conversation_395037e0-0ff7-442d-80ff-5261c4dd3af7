<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\PeriodoPiso;
use App\Models\Piso;
use App\Cliente;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Carbon\Carbon;

class PeriodoPisoController extends Controller
{
    /**
     * Listar periodos de alquiler de pisos
     */
    public function index(Request $request): JsonResponse
    {
        $query = PeriodoPiso::with(['piso', 'cliente']);

        // Filtros
        if ($request->has('piso_id')) {
            $query->where('id_piso', $request->piso_id);
        }

        if ($request->has('cliente_id')) {
            $query->where('id_cliente', $request->cliente_id);
        }

        if ($request->has('activos') && $request->boolean('activos')) {
            $query->whereNull('date_to');
        }

        if ($request->has('finalizados') && $request->boolean('finalizados')) {
            $query->whereNotNull('date_to');
        }

        $periodos = $query->paginate($request->get('per_page', 15));

        return response()->json($periodos);
    }

    /**
     * Crear un nuevo periodo de alquiler de piso
     */
    public function store(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'id_piso' => 'required|exists:pisos,id',
            'id_cliente' => 'required|exists:clientes,id',
            'date_from' => 'required|date',
            'date_to' => 'nullable|date|after:date_from',
            'fianza' => 'required|numeric|min:0',
            'precio_mensual' => 'required|numeric|min:0'
        ]);

        // Verificar que el piso esté disponible
        $piso = Piso::findOrFail($validated['id_piso']);
        
        if (!$piso->disponible) {
            return response()->json([
                'message' => 'El piso no está disponible para alquilar'
            ], 422);
        }

        // Crear el periodo
        $periodo = PeriodoPiso::create($validated);

        // Actualizar estado del piso
        $piso->update(['disponible' => false]);

        // Actualizar cliente si es posible cliente
        $cliente = Cliente::findOrFail($validated['id_cliente']);
        if ($cliente->posible_cliente) {
            $cliente->update(['posible_cliente' => false, 'status' => true]);
        }

        return response()->json($periodo->load(['piso', 'cliente']), 201);
    }

    /**
     * Mostrar un periodo específico
     */
    public function show(PeriodoPiso $periodoPiso): JsonResponse
    {
        return response()->json($periodoPiso->load(['piso', 'cliente', 'alquileres']));
    }

    /**
     * Actualizar un periodo de alquiler
     */
    public function update(Request $request, PeriodoPiso $periodoPiso): JsonResponse
    {
        $validated = $request->validate([
            'date_from' => 'sometimes|date',
            'date_to' => 'nullable|date|after:date_from',
            'fianza' => 'sometimes|numeric|min:0',
            'precio_mensual' => 'sometimes|numeric|min:0'
        ]);

        $periodoPiso->update($validated);

        // Si se finaliza el periodo, liberar el piso
        if (isset($validated['date_to']) && $validated['date_to']) {
            $piso = Piso::findOrFail($periodoPiso->id_piso);
            $piso->update(['disponible' => true]);
        }

        return response()->json($periodoPiso->load(['piso', 'cliente']));
    }

    /**
     * Finalizar un periodo de alquiler
     */
    public function finalizar(Request $request, PeriodoPiso $periodoPiso): JsonResponse
    {
        $validated = $request->validate([
            'date_to' => 'required|date',
            'observaciones' => 'nullable|string'
        ]);

        $periodoPiso->update([
            'date_to' => $validated['date_to'],
            'observaciones' => $validated['observaciones'] ?? null
        ]);

        // Liberar el piso
        $piso = Piso::findOrFail($periodoPiso->id_piso);
        $piso->update(['disponible' => true]);

        return response()->json($periodoPiso->load(['piso', 'cliente']));
    }

    /**
     * Eliminar un periodo de alquiler
     */
    public function destroy(PeriodoPiso $periodoPiso): JsonResponse
    {
        // Verificar que no tenga alquileres asociados
        if ($periodoPiso->alquileres()->count() > 0) {
            return response()->json([
                'message' => 'No se puede eliminar un periodo con alquileres asociados'
            ], 422);
        }

        // Liberar el piso si el periodo estaba activo
        if (!$periodoPiso->date_to) {
            $piso = Piso::findOrFail($periodoPiso->id_piso);
            $piso->update(['disponible' => true]);
        }

        $periodoPiso->delete();

        return response()->json(['message' => 'Periodo eliminado correctamente']);
    }
}