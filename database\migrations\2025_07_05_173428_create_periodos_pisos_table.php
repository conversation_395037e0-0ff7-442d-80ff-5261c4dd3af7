<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('periodos_pisos', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('id_piso');
            $table->unsignedBigInteger('id_cliente');
            $table->date('fecha_inicio');
            $table->date('fecha_fin')->nullable();
            $table->decimal('precio_mensual', 10, 2);
            $table->decimal('fianza_pagada', 10, 2)->nullable();
            $table->date('fecha_fianza')->nullable();
            $table->longText('observaciones')->nullable();
            $table->enum('estado', ['activo', 'finalizado', 'suspendido'])->default('activo');
            $table->json('condiciones_especiales')->nullable(); // mascotas, fumador, etc.

            $table->foreign('id_piso')->references('id')->on('pisos')->onDelete('cascade');
            $table->foreign('id_cliente')->references('id')->on('clientes')->onDelete('cascade');

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('periodos_pisos');
    }
};

