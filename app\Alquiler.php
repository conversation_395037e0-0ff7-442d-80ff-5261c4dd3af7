<?php

namespace App;

use Illuminate\Database\Eloquent\Model;
use Carbon\Carbon;

class Alquiler extends Model
{
    protected $table = 'alquileres';

    protected $fillable = [
        'M', 'A', 'id_cliente', 'id_periodo', 'valor', 'valor_pago',
        'alquilable_id', 'alquilable_type', 'estado_pago', 'fecha_vencimiento',
        'recargo_mora', 'observaciones'
    ];

    protected $casts = [
        'valor' => 'decimal:2',
        'valor_pago' => 'decimal:2',
        'recargo_mora' => 'decimal:2',
        'fecha_vencimiento' => 'date'
    ];

    // Relaciones
    public function cliente() {
        return $this->belongsTo(Cliente::class, 'id_cliente');
    }

    public function periodo() {
        return $this->belongsTo(Periodo::class, 'id_periodo');
    }

    // Relación polimórfica para trasteros y pisos
    public function alquilable() {
        return $this->morphTo();
    }

    public function pagos() {
        return $this->hasMany(Pago::class, 'id_alquiler');
    }

    // Scopes
    public function scopePendientes($query) {
        return $query->where('estado_pago', 'pendiente');
    }

    public function scopeVencidos($query) {
        return $query->where('estado_pago', 'vencido')
                    ->orWhere(function($q) {
                        $q->where('estado_pago', 'pendiente')
                          ->where('fecha_vencimiento', '<', Carbon::now());
                    });
    }

    public function scopeAlDia($query) {
        return $query->where('estado_pago', 'al_dia');
    }

    public function scopeParciales($query) {
        return $query->where('estado_pago', 'parcial');
    }

    // Métodos auxiliares
    public function getSaldoPendienteAttribute()
    {
        return $this->valor - $this->valor_pago + $this->recargo_mora;
    }

    public function estaVencido()
    {
        return $this->fecha_vencimiento && $this->fecha_vencimiento->isPast() &&
               in_array($this->estado_pago, ['pendiente', 'parcial']);
    }

    public function actualizarEstadoPago()
    {
        if ($this->valor_pago >= $this->valor) {
            $this->estado_pago = 'al_dia';
        } elseif ($this->valor_pago > 0) {
            $this->estado_pago = 'parcial';
        } elseif ($this->estaVencido()) {
            $this->estado_pago = 'vencido';
        } else {
            $this->estado_pago = 'pendiente';
        }

        $this->save();
    }
}

