<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Documento extends Model
{
    use HasFactory;

    protected $fillable = [
        'nombre',
        'tipo_documento',
        'archivo_path',
        'archivo_nombre_original',
        'mime_type',
        'tamano_archivo',
        'fecha_documento',
        'importe',
        'descripcion',
        'estado',
        'documentable_id',
        'documentable_type',
        'subido_por',
        'documento_padre_id',
        'motivo_cambio',
        'version',
        'ruta_archivo',
        'tipo_archivo'
    ];

    protected $casts = [
        'fecha_documento' => 'date',
        'importe' => 'decimal:2'
    ];

    // Relación polimórfica
    public function documentable()
    {
        return $this->morphTo();
    }

    // Usuario que subió el documento
    public function usuario()
    {
        return $this->belongsTo(User::class, 'subido_por');
    }

    // Relaciones para versionado
    public function documentoPadre()
    {
        return $this->belongsTo(Documento::class, 'documento_padre_id');
    }

    public function versiones()
    {
        return $this->hasMany(Documento::class, 'documento_padre_id');
    }

    // Scopes
    public function scopePorTipo($query, $tipo)
    {
        return $query->where('tipo_documento', $tipo);
    }

    public function scopePendientes($query)
    {
        return $query->where('estado', 'pendiente');
    }

    public function scopeProcesados($query)
    {
        return $query->where('estado', 'procesado');
    }

    // Métodos auxiliares
    public function getUrlAttribute()
    {
        return asset('storage/' . $this->archivo_path);
    }

    public function getTamanoFormateadoAttribute()
    {
        $bytes = $this->tamano_archivo;
        $units = ['B', 'KB', 'MB', 'GB'];

        for ($i = 0; $bytes > 1024; $i++) {
            $bytes /= 1024;
        }

        return round($bytes, 2) . ' ' . $units[$i];
    }
}
