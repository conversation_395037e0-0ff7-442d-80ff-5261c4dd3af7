<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Piso;
use App\Trastero;
use App\Cliente;
use App\Alquiler;
use App\Models\GastoEdificio;
use App\Models\Documento;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;

class DashboardController extends Controller
{
    /**
     * Get dashboard summary
     */
    public function resumen(): JsonResponse
    {
        $data = [
            'propiedades' => [
                'trasteros_total' => Trastero::count(),
                'trasteros_ocupados' => Trastero::where('ocupado', true)->count(),
                'trasteros_disponibles' => Trastero::where('ocupado', false)->count(),
                'pisos_total' => Piso::count(),
                'pisos_ocupados' => Piso::where('disponible', false)->count(),
                'pisos_disponibles' => Piso::where('disponible', true)->count(),
            ],
            'clientes' => [
                'total' => Cliente::count(),
                'activos' => Cliente::where('status', true)->count(),
                'posibles' => Cliente::where('posible_cliente', true)->count(),
                'confirmados' => Cliente::where('posible_cliente', false)->count(),
            ],
            'pagos' => [
                'pendientes' => Alquiler::where('estado_pago', 'pendiente')->count(),
                'vencidos' => Alquiler::where('estado_pago', 'vencido')->count(),
                'al_dia' => Alquiler::where('estado_pago', 'al_dia')->count(),
                'parciales' => Alquiler::where('estado_pago', 'parcial')->count(),
            ],
            'gastos' => [
                'pendientes' => GastoEdificio::where('estado_pago', 'pendiente')->count(),
                'vencidos' => GastoEdificio::where('estado_pago', 'vencido')->count(),
                'pagados' => GastoEdificio::where('estado_pago', 'pagado')->count(),
            ],
            'documentos' => [
                'total' => Documento::count(),
                'pendientes' => Documento::where('estado', 'pendiente')->count(),
                'procesados' => Documento::where('estado', 'procesado')->count(),
                'archivados' => Documento::where('estado', 'archivado')->count(),
            ]
        ];

        return response()->json($data);
    }

    /**
     * Get pending payments summary
     */
    public function pagosPendientes(): JsonResponse
    {
        $alquileresPendientes = Alquiler::with(['cliente', 'alquilable'])
                                       ->whereIn('estado_pago', ['pendiente', 'vencido', 'parcial'])
                                       ->orderBy('fecha_vencimiento', 'asc')
                                       ->limit(10)
                                       ->get();

        $gastosPendientes = GastoEdificio::where('estado_pago', 'pendiente')
                                        ->orderBy('fecha_vencimiento', 'asc')
                                        ->limit(10)
                                        ->get();

        $totalPendiente = Alquiler::whereIn('estado_pago', ['pendiente', 'vencido', 'parcial'])
                                 ->sum(DB::raw('valor - valor_pago + COALESCE(recargo_mora, 0)'));

        return response()->json([
            'alquileres_pendientes' => $alquileresPendientes,
            'gastos_pendientes' => $gastosPendientes,
            'total_pendiente' => $totalPendiente,
        ]);
    }

    /**
     * Get occupation statistics
     */
    public function estadisticasOcupacion(): JsonResponse
    {
        $trasteros = [
            'total' => Trastero::count(),
            'ocupados' => Trastero::where('ocupado', true)->count(),
            'disponibles' => Trastero::where('ocupado', false)->count(),
        ];

        $pisos = [
            'total' => Piso::count(),
            'ocupados' => Piso::where('disponible', false)->count(),
            'disponibles' => Piso::where('disponible', true)->count(),
        ];

        $trasteros['porcentaje_ocupacion'] = $trasteros['total'] > 0
            ? round(($trasteros['ocupados'] / $trasteros['total']) * 100, 2)
            : 0;

        $pisos['porcentaje_ocupacion'] = $pisos['total'] > 0
            ? round(($pisos['ocupados'] / $pisos['total']) * 100, 2)
            : 0;

        return response()->json([
            'trasteros' => $trasteros,
            'pisos' => $pisos,
        ]);
    }

    /**
     * Get income statistics
     */
    public function estadisticasIngresos(Request $request): JsonResponse
    {
        $meses = $request->get('meses', 12); // Por defecto últimos 12 meses
        $fechaInicio = Carbon::now()->subMonths($meses);

        // Ingresos por alquileres
        $ingresosPorMes = Alquiler::select(
                DB::raw('YEAR(created_at) as ano'),
                DB::raw('MONTH(created_at) as mes'),
                DB::raw('SUM(valor_pago) as total_ingresos'),
                DB::raw('COUNT(*) as total_alquileres')
            )
            ->where('created_at', '>=', $fechaInicio)
            ->where('estado_pago', 'al_dia')
            ->groupBy('ano', 'mes')
            ->orderBy('ano', 'desc')
            ->orderBy('mes', 'desc')
            ->get();

        // Gastos por mes
        $gastosPorMes = GastoEdificio::select(
                DB::raw('YEAR(fecha_gasto) as ano'),
                DB::raw('MONTH(fecha_gasto) as mes'),
                DB::raw('SUM(importe) as total_gastos'),
                DB::raw('COUNT(*) as total_facturas')
            )
            ->where('fecha_gasto', '>=', $fechaInicio)
            ->where('estado_pago', 'pagado')
            ->groupBy('ano', 'mes')
            ->orderBy('ano', 'desc')
            ->orderBy('mes', 'desc')
            ->get();

        // Totales
        $totalIngresos = Alquiler::where('estado_pago', 'al_dia')
                                ->where('created_at', '>=', $fechaInicio)
                                ->sum('valor_pago');

        $totalGastos = GastoEdificio::where('estado_pago', 'pagado')
                                  ->where('fecha_gasto', '>=', $fechaInicio)
                                  ->sum('importe');

        return response()->json([
            'ingresos_por_mes' => $ingresosPorMes,
            'gastos_por_mes' => $gastosPorMes,
            'total_ingresos' => $totalIngresos,
            'total_gastos' => $totalGastos,
            'beneficio_neto' => $totalIngresos - $totalGastos,
            'periodo_meses' => $meses,
        ]);
    }

    /**
     * Reporte detallado de ingresos
     */
    public function reporteIngresosDetallado(Request $request): JsonResponse
    {
        $fechaInicio = $request->get('fecha_inicio', Carbon::now()->startOfYear());
        $fechaFin = $request->get('fecha_fin', Carbon::now()->endOfYear());

        $ingresosPorMes = Alquiler::selectRaw('
                A as año,
                M as mes,
                SUM(valor_pago) as total_pagado,
                SUM(valor) as total_facturado,
                COUNT(*) as total_alquileres,
                SUM(CASE WHEN estado_pago = "al_dia" THEN 1 ELSE 0 END) as pagados,
                SUM(CASE WHEN estado_pago = "pendiente" THEN 1 ELSE 0 END) as pendientes
            ')
            ->whereRaw('CONCAT(A, "-", LPAD(M, 2, "0"), "-01") BETWEEN ? AND ?', [$fechaInicio, $fechaFin])
            ->groupBy('A', 'M')
            ->orderBy('A', 'desc')
            ->orderBy('M', 'desc')
            ->get();

        $resumen = [
            'total_facturado' => $ingresosPorMes->sum('total_facturado'),
            'total_pagado' => $ingresosPorMes->sum('total_pagado'),
            'total_pendiente' => $ingresosPorMes->sum('total_facturado') - $ingresosPorMes->sum('total_pagado'),
            'porcentaje_cobro' => $ingresosPorMes->sum('total_facturado') > 0
                ? round(($ingresosPorMes->sum('total_pagado') / $ingresosPorMes->sum('total_facturado')) * 100, 2)
                : 0
        ];

        return response()->json([
            'ingresos_por_mes' => $ingresosPorMes,
            'resumen' => $resumen
        ]);
    }

    /**
     * Ocupación histórica
     */
    public function ocupacionHistorica(Request $request): JsonResponse
    {
        $meses = $request->get('meses', 12);
        $fechaInicio = Carbon::now()->subMonths($meses)->startOfMonth();

        $ocupacionTrasteros = [];
        $ocupacionPisos = [];

        for ($i = 0; $i < $meses; $i++) {
            $fecha = $fechaInicio->copy()->addMonths($i);
            $fechaStr = $fecha->format('Y-m');

            // Trasteros ocupados en esa fecha
            $trasterosOcupados = \App\Periodo::whereHas('trastero')
                ->where('date_from', '<=', $fecha->endOfMonth())
                ->where(function($q) use ($fecha) {
                    $q->whereNull('date_to')
                      ->orWhere('date_to', '>=', $fecha->startOfMonth());
                })
                ->count();

            $totalTrasteros = Trastero::count();

            // Pisos ocupados en esa fecha
            $pisosOcupados = \App\Models\PeriodoPiso::whereHas('piso')
                ->where('date_from', '<=', $fecha->endOfMonth())
                ->where(function($q) use ($fecha) {
                    $q->whereNull('date_to')
                      ->orWhere('date_to', '>=', $fecha->startOfMonth());
                })
                ->count();

            $totalPisos = \App\Models\Piso::count();

            $ocupacionTrasteros[] = [
                'fecha' => $fechaStr,
                'ocupados' => $trasterosOcupados,
                'total' => $totalTrasteros,
                'porcentaje' => $totalTrasteros > 0 ? round(($trasterosOcupados / $totalTrasteros) * 100, 2) : 0
            ];

            $ocupacionPisos[] = [
                'fecha' => $fechaStr,
                'ocupados' => $pisosOcupados,
                'total' => $totalPisos,
                'porcentaje' => $totalPisos > 0 ? round(($pisosOcupados / $totalPisos) * 100, 2) : 0
            ];
        }

        return response()->json([
            'trasteros' => $ocupacionTrasteros,
            'pisos' => $ocupacionPisos
        ]);
    }

    /**
     * Clientes morosos
     */
    public function clientesMorosos(): JsonResponse
    {
        $fechaLimite = Carbon::now()->subDays(30);

        $clientesMorosos = Cliente::whereHas('alquileres', function($q) use ($fechaLimite) {
                $q->where('estado_pago', 'pendiente')
                  ->whereRaw('CONCAT(A, "-", LPAD(M, 2, "0"), "-01") < ?', [$fechaLimite->format('Y-m-01')]);
            })
            ->with(['alquileres' => function($q) use ($fechaLimite) {
                $q->where('estado_pago', 'pendiente')
                  ->whereRaw('CONCAT(A, "-", LPAD(M, 2, "0"), "-01") < ?', [$fechaLimite->format('Y-m-01')])
                  ->orderBy('A', 'desc')
                  ->orderBy('M', 'desc');
            }])
            ->get()
            ->map(function($cliente) {
                $deudaTotal = $cliente->alquileres->sum('valor');
                $mesesMoroso = $cliente->alquileres->count();

                return [
                    'id' => $cliente->id,
                    'nombre' => $cliente->nombre . ' ' . $cliente->apellido,
                    'telefono' => $cliente->telefono ?? 'No disponible',
                    'deuda_total' => $deudaTotal,
                    'meses_moroso' => $mesesMoroso,
                    'ultimo_pago' => $cliente->alquileres->first() ?
                        $cliente->alquileres->first()->A . '-' . str_pad($cliente->alquileres->first()->M, 2, '0', STR_PAD_LEFT) : null
                ];
            });

        return response()->json($clientesMorosos);
    }

    /**
     * Reporte de rentabilidad
     */
    public function reporteRentabilidad(Request $request): JsonResponse
    {
        $año = $request->get('año', Carbon::now()->year);

        // Ingresos por alquileres
        $ingresos = Alquiler::where('A', $año)
                           ->sum('valor_pago');

        // Gastos del edificio
        $gastos = GastoEdificio::whereYear('fecha_gasto', $año)
                              ->sum('importe');

        // Rentabilidad por tipo
        $rentabilidadTrasteros = Alquiler::join('periodos', 'alquileres.id_periodo', '=', 'periodos.id')
                                        ->join('trasteros', 'periodos.id_trastero', '=', 'trasteros.id')
                                        ->where('alquileres.A', $año)
                                        ->selectRaw('
                                            SUM(alquileres.valor_pago) as ingresos,
                                            COUNT(DISTINCT trasteros.id) as unidades_activas,
                                            AVG(trasteros.price) as precio_promedio
                                        ')
                                        ->first();

        return response()->json([
            'año' => $año,
            'resumen' => [
                'ingresos_totales' => $ingresos,
                'gastos_totales' => $gastos,
                'beneficio_neto' => $ingresos - $gastos,
                'margen_beneficio' => $ingresos > 0 ? round((($ingresos - $gastos) / $ingresos) * 100, 2) : 0
            ],
            'trasteros' => $rentabilidadTrasteros
        ]);
    }

    /**
     * Exportar reporte
     */
    public function exportarReporte(Request $request, string $tipo): JsonResponse
    {
        // Esta función devolvería la URL del archivo generado
        // Por ahora devolvemos un placeholder
        return response()->json([
            'message' => 'Funcionalidad de exportación en desarrollo',
            'tipo' => $tipo,
            'url' => null
        ]);
    }

    /**
     * Notificaciones del sistema
     */
    public function notificaciones(): JsonResponse
    {
        $notificaciones = [];

        // Pagos vencidos
        $pagosVencidos = Alquiler::where('estado_pago', 'pendiente')
                               ->whereRaw('CONCAT(A, "-", LPAD(M, 2, "0"), "-01") < ?', [Carbon::now()->subDays(5)->format('Y-m-01')])
                               ->count();

        if ($pagosVencidos > 0) {
            $notificaciones[] = [
                'id' => 'pagos_vencidos',
                'tipo' => 'warning',
                'titulo' => 'Pagos Vencidos',
                'mensaje' => "Tienes {$pagosVencidos} pagos vencidos pendientes",
                'fecha' => Carbon::now(),
                'leida' => false
            ];
        }

        // Documentos pendientes
        $documentosPendientes = Documento::where('estado', 'pendiente')->count();

        if ($documentosPendientes > 0) {
            $notificaciones[] = [
                'id' => 'documentos_pendientes',
                'tipo' => 'info',
                'titulo' => 'Documentos Pendientes',
                'mensaje' => "Tienes {$documentosPendientes} documentos pendientes de procesar",
                'fecha' => Carbon::now(),
                'leida' => false
            ];
        }

        return response()->json($notificaciones);
    }

    /**
     * Marcar notificación como leída
     */
    public function marcarLeida(string $id): JsonResponse
    {
        // Por ahora solo devolvemos éxito
        // En una implementación real, esto se guardaría en base de datos
        return response()->json(['message' => 'Notificación marcada como leída']);
    }

    /**
     * Enviar recordatorio
     */
    public function enviarRecordatorio(string $tipo, int $id): JsonResponse
    {
        // Placeholder para funcionalidad de envío de recordatorios
        return response()->json([
            'message' => 'Recordatorio enviado correctamente',
            'tipo' => $tipo,
            'id' => $id
        ]);
    }

    /**
     * Búsqueda global
     */
    public function busquedaGlobal(Request $request): JsonResponse
    {
        $query = $request->get('q');

        if (!$query) {
            return response()->json(['resultados' => []]);
        }

        $resultados = [];

        // Buscar en trasteros
        $trasteros = Trastero::where('num', 'like', "%{$query}%")
                            ->orWhere('titulo', 'like', "%{$query}%")
                            ->limit(5)
                            ->get()
                            ->map(function($trastero) {
                                return [
                                    'tipo' => 'trastero',
                                    'id' => $trastero->id,
                                    'titulo' => "Trastero {$trastero->num}",
                                    'subtitulo' => $trastero->titulo,
                                    'url' => "/trasteros/{$trastero->id}"
                                ];
                            });

        // Buscar en pisos
        $pisos = \App\Models\Piso::where('numero_piso', 'like', "%{$query}%")
                    ->orWhere('direccion', 'like', "%{$query}%")
                    ->limit(5)
                    ->get()
                    ->map(function($piso) {
                        return [
                            'tipo' => 'piso',
                            'id' => $piso->id,
                            'titulo' => "Piso {$piso->numero_piso}",
                            'subtitulo' => $piso->direccion,
                            'url' => "/pisos/{$piso->id}"
                        ];
                    });

        // Buscar en clientes
        $clientes = Cliente::where('nombre', 'like', "%{$query}%")
                          ->orWhere('apellido', 'like', "%{$query}%")
                          ->orWhere('documento', 'like', "%{$query}%")
                          ->limit(5)
                          ->get()
                          ->map(function($cliente) {
                              return [
                                  'tipo' => 'cliente',
                                  'id' => $cliente->id,
                                  'titulo' => $cliente->nombre . ' ' . $cliente->apellido,
                                  'subtitulo' => $cliente->documento,
                                  'url' => "/clientes/{$cliente->id}"
                              ];
                          });

        $resultados = $trasteros->concat($pisos)->concat($clientes);

        return response()->json(['resultados' => $resultados]);
    }
}
