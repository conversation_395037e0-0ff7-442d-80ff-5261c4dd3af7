<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class PeriodoResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'id_cliente' => $this->id_cliente,
            'id_trastero' => $this->id_trastero,
            'date_from' => $this->date_from,
            'date_to' => $this->date_to,
            'activo' => is_null($this->date_to),
            'cliente' => new ClienteResource($this->whenLoaded('cliente')),
            'trastero' => new TrasteroResource($this->whenLoaded('trastero')),
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
        ];
    }
}
